// src/app/test-fouc/page.tsx
"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

export default function TestFOUCPage() {
    const [reloadCount, setReloadCount] = useState(0);
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true);
        // Get reload count from sessionStorage
        const count = parseInt(sessionStorage.getItem("reloadCount") || "0");
        setReloadCount(count);
    }, []);

    const handleReload = () => {
        const newCount = reloadCount + 1;
        sessionStorage.setItem("reloadCount", newCount.toString());
        window.location.reload();
    };

    const handleScrollToFooter = () => {
        const footer = document.querySelector("footer");
        if (footer) {
            footer.scrollIntoView({ behavior: "smooth" });
        }
    };

    const handleScrollToTop = () => {
        window.scrollTo({ top: 0, behavior: "smooth" });
    };

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-4xl font-bold mb-8 text-center">
                    Footer FOUC Test Page
                </h1>
                
                <div className="bg-card p-6 rounded-lg mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Test Instructions</h2>
                    <ol className="list-decimal list-inside space-y-2 text-muted-foreground">
                        <li>Reload this page multiple times and observe the footer</li>
                        <li>Check if the footer flashes or changes appearance during page load</li>
                        <li>Test with different scroll positions</li>
                        <li>Try switching between light and dark themes</li>
                        <li>The footer should appear consistently without any visual flash</li>
                    </ol>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    <Button onClick={handleReload} variant="default" size="lg">
                        Reload Page (Count: {isClient ? reloadCount : 0})
                    </Button>
                    <Button onClick={handleScrollToFooter} variant="outline" size="lg">
                        Scroll to Footer
                    </Button>
                </div>

                <div className="bg-muted p-6 rounded-lg mb-8">
                    <h3 className="text-xl font-semibold mb-4">What to Look For</h3>
                    <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                        <li><strong>No Flash:</strong> Footer should not briefly appear with different styling</li>
                        <li><strong>Consistent Background:</strong> Footer background should be stable from the start</li>
                        <li><strong>Smooth Loading:</strong> No sudden changes in footer appearance</li>
                        <li><strong>Theme Consistency:</strong> Footer should respect the current theme immediately</li>
                    </ul>
                </div>

                {/* Add some content to make the page scrollable */}
                <div className="space-y-8">
                    {Array.from({ length: 10 }, (_, i) => (
                        <div key={i} className="bg-card p-6 rounded-lg">
                            <h3 className="text-xl font-semibold mb-2">Test Section {i + 1}</h3>
                            <p className="text-muted-foreground">
                                This is test content to make the page scrollable. 
                                The footer should remain stable regardless of scroll position 
                                when the page is reloaded. Lorem ipsum dolor sit amet, 
                                consectetur adipiscing elit. Sed do eiusmod tempor incididunt 
                                ut labore et dolore magna aliqua.
                            </p>
                        </div>
                    ))}
                </div>

                <div className="mt-8 text-center">
                    <Button onClick={handleScrollToTop} variant="secondary">
                        Back to Top
                    </Button>
                </div>
            </div>
        </div>
    );
}
