@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer base {
    :root {
        --background: hsl(60 60% 93%);
        --foreground: hsl(224 71.4% 4.1%);
        --card: hsl(60 16% 96%);
        --card-foreground: hsl(224 71.4% 4.1%);
        --popover: hsl(60 29% 92%);
        --popover-foreground: hsl(224 71.4% 4.1%);
        --primary: hsl(174 71% 57%);
        --hero-title: hsl(170 14% 90%);
        --primary-foreground: hsl(224 71.4% 4.1%);
        --secondary: hsl(60 4.8% 95.9%);
        --secondary-foreground: hsl(222.2 84% 4.9%);
        --muted: hsl(60 4.8% 95.9%);
        --muted-foreground: hsl(215.4 16.3% 36%);
        --accent: hsl(16 100% 66%);
        --accent-foreground: hsl(0 0% 98%);
        --destructive: hsl(0 84.2% 60.2%);
        --destructive-foreground: hsl(0 0% 98%);
        --border: hsl(60 5.9% 90%);
        --input: hsl(60 5.9% 90%);
        --ring: hsl(174 71% 57%);
        --chart-1: hsl(12 76% 61%);
        --chart-2: hsl(173 58% 39%);
        --chart-3: hsl(197 37% 24%);
        --chart-4: hsl(43 74% 66%);
        --chart-5: hsl(27 87% 67%);
        --radius: 0.5rem;
        --sidebar-background: hsl(0 0% 98%);
        --sidebar-foreground: hsl(240 5.3% 26.1%);
        --sidebar-primary: hsl(240 5.9% 10%);
        --sidebar-primary-foreground: hsl(0 0% 98%);
        --sidebar-accent: hsl(240 4.8% 95.9%);
        --sidebar-accent-foreground: hsl(240 5.9% 10%);
        --sidebar-border: hsl(220 13% 91%);
        --sidebar-ring: hsl(217.2 91.2% 59.8%);
        --bg-alternate: hsl(220 13% 87%); /* Light slate gray for light theme */
        --special-card-fg: hsl(224 71.4% 4.1%);
    }

    .dark {
        --background: hsl(200 60% 9%);
        --foreground: hsl(60 9.1% 97.8%);
        --card: hsl(210 16% 15%);
        --card-foreground: hsl(60 9.1% 97.8%);
        --popover: hsl(214, 41%, 12%);
        --popover-foreground: hsl(60 9.1% 97.8%);
        --primary: hsl(180 66% 54%);
        --primary-foreground: hsl(224 71.4% 4.1%);
        --secondary: hsl(197 51% 12%);
        --secondary-foreground: hsl(60 9.1% 97.8%);
        --muted: hsl(217.2 32.6% 17.5%);
        --muted-foreground: hsl(215 20.2% 65.1%);
        --accent: hsl(16 100% 66%);
        --accent-foreground: hsl(0 0% 98%);
        --destructive: hsl(0 62.8% 30.6%);
        --destructive-foreground: hsl(0 0% 98%);
        --border: hsl(217.2 32.6% 32%);
        --input: hsl(217.2 32.6% 17.5%);
        --ring: hsl(30, 100%, 57%);
        --chart-1: hsl(220 70% 50%);
        --chart-2: hsl(160 60% 45%);
        --chart-3: hsl(30 80% 55%);
        --chart-4: hsl(280 65% 60%);
        --chart-5: hsl(340 75% 55%);
        --sidebar-background: hsl(240 5.9% 10%);
        --sidebar-foreground: hsl(240 4.8% 95.9%);
        --sidebar-primary: hsl(224.3 76.3% 48%);
        --sidebar-primary-foreground: hsl(0 0% 100%);
        --sidebar-accent: hsl(240 3.7% 15.9%);
        --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
        --sidebar-border: hsl(240 3.7% 15.9%);
        --sidebar-ring: hsl(217.2 91.2% 59.8%);
        --bg-alternate: hsl(214 15% 16%); /* Dark slate gray for dark theme */
        --special-card-fg: hsl(0 5% 88%);
    }
}

@layer base {
    * {
        border-color: var(--border);
    }
    html {
        scroll-behavior: smooth;
    }

    body {
        background-color: var(--background);
        color: var(--foreground);
    }
}

/* Custom utilities for GradientButton text-shadow */
@layer utilities {
    .text-shadow-md {
        text-shadow: 1px 3px 1px #1f1f1f;
    }

    .text-shadow-sm {
        text-shadow: 1px 2px 1px rgba(31, 31, 31, 0.8);
    }

    .text-shadow-lg {
        text-shadow: 1px 5px 1px rgba(31, 31, 31, 0.8);
    }

    .text-shadow-xs {
        text-shadow: 1px 1px 1px rgba(31, 30, 30, 0.8);
    }

    .text-shadow-none {
        text-shadow: none;
    }

    /* Custom drop-shadow utilities */
    .icon-shadow-sm {
        filter: drop-shadow(1px 1px 1px #1f1f1f);
    }

    .icon-shadow-md {
        filter: drop-shadow(1px 2px 1px #1f1f1f);
    }

    .icon-shadow-lg {
        filter: drop-shadow(1px 3px 1px #1f1f1f);
    }

    /* Prevent FOUC for footer component */
    footer {
        opacity: 0;
        transition: opacity 300ms ease-in-out;
        /* Prevent layout shift during loading */
        min-height: 200px;
    }

    /* Show footer after hydration - multiple targeting approaches for reliability */
    footer[data-hydrated="true"],
    html[data-hydrated="true"] footer {
        opacity: 1;
    }

    /* Fallback: Show footer immediately when JavaScript is disabled */
    html:not(.js-enabled) footer {
        opacity: 1 !important;
        transition: none;
    }

    /* Additional fallback: Show footer after a delay if hydration fails */
    @media (prefers-reduced-motion: no-preference) {
        footer {
            animation: footer-fallback-show 0.3s ease-in-out 2s forwards;
        }

        footer[data-hydrated="true"] {
            animation: none;
        }
    }

    @keyframes footer-fallback-show {
        to {
            opacity: 1;
        }
    }
}
