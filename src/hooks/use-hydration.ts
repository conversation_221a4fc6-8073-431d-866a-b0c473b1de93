// src/hooks/use-hydration.ts
"use client";
import { useState, useEffect } from "react";

/**
 * Custom hook to handle hydration state and prevent FOUC
 * Returns true only after the component has been hydrated on the client
 */
export function useHydration(): boolean {
    const [isHydrated, setIsHydrated] = useState(false);

    useEffect(() => {
        // Use requestAnimationFrame to ensure DOM is ready
        const frame = requestAnimationFrame(() => {
            setIsHydrated(true);
        });

        return () => cancelAnimationFrame(frame);
    }, []);

    return isHydrated;
}

/**
 * Custom hook that combines hydration state with theme detection
 * Prevents FOUC by ensuring theme is properly resolved before rendering
 */
export function useHydratedTheme() {
    const [isReady, setIsReady] = useState(false);
    const [resolvedTheme, setResolvedTheme] = useState<string | undefined>(
        undefined,
    );

    useEffect(() => {
        // Check if we're in the browser
        if (typeof window === "undefined") return;

        // Get the current theme from the document class
        const isDark = document.documentElement.classList.contains("dark");
        const theme = isDark ? "dark" : "light";

        setResolvedTheme(theme);

        // Use requestAnimationFrame to ensure DOM is ready and theme is stable
        const frame = requestAnimationFrame(() => {
            setIsReady(true);
        });

        // Also listen for theme changes to update immediately
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (
                    mutation.type === "attributes" &&
                    mutation.attributeName === "class"
                ) {
                    const isDarkNow =
                        document.documentElement.classList.contains("dark");
                    const newTheme = isDarkNow ? "dark" : "light";
                    if (newTheme !== resolvedTheme) {
                        setResolvedTheme(newTheme);
                    }
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ["class"],
        });

        return () => {
            cancelAnimationFrame(frame);
            observer.disconnect();
        };
    }, [resolvedTheme]);

    return { isReady, resolvedTheme, isDark: resolvedTheme === "dark" };
}
